# Bugly自动登录工具

这是一个优化的Bugly自动登录工具，支持QQ账号密码登录并自动保存cookies。

## 功能特点

- 直接访问Bugly OAuth登录页面，无需重定向
- 自动处理QQ登录流程
- 智能保存和管理cookies
- 支持交互式登录（处理验证码等情况）
- 清晰的代码结构，易于维护和扩展

## 文件结构

```
├── main.py              # 主入口文件
├── bugly_login.py       # 核心登录类
├── cookie_manager.py    # Cookie管理器
├── config.json          # 配置文件
├── example.py           # 使用示例
└── README.md           # 说明文档
```

## 安装依赖

```bash
pip install playwright
playwright install chromium
```

## 配置

在 `config.json` 中配置你的QQ账号信息：

```json
{
  "qq_login": {
    "username": "你的QQ号",
    "password": "你的QQ密码",
    "enable_auto_login": true
  },
  "scraper_settings": {
    "headless": false,
    "timeout": 30000,
    "max_scroll_attempts": 5
  }
}
```

## 使用方法

### 命令行使用

```bash
# 自动登录并保存cookies
python main.py

# 交互式登录（保持浏览器打开，适合需要手动处理验证码的情况）
python main.py --interactive

# 清除保存的cookies
python main.py --clear
```

### 代码中使用

```python
import asyncio
from bugly_login import BuglyLogin

async def login_example():
    login = BuglyLogin()
    success = await login.login()
    
    if success:
        print("登录成功！")
    else:
        print("登录失败")

asyncio.run(login_example())
```

## Cookie管理

工具会自动将登录后的cookies保存到 `bugly_cookies.json` 文件中，下次登录时会自动加载这些cookies以避免重复登录。

### Cookie管理功能

- 自动保存登录后的cookies
- 智能检查cookies有效性（默认24小时）
- 支持手动清除cookies
- 过滤和保存相关域名的cookies

## 优化特点

✅ **直接访问OAuth登录页面** - 使用指定的QQ OAuth URL，避免重定向复杂性
✅ **智能Cookie管理** - 自动保存和加载cookies，避免重复登录
✅ **清晰的代码结构** - 模块化设计，易于维护和扩展
✅ **删除测试代码** - 移除所有截图和调试功能，只保留核心登录功能
✅ **错误处理优化** - 改进超时设置和页面加载策略

## 注意事项

1. 请确保QQ账号和密码正确配置在 `config.json` 中
2. 如果遇到验证码，建议使用 `--interactive` 模式手动处理
3. cookies默认有效期为24小时，过期后会自动重新登录
4. 工具使用Edge浏览器内核，请确保系统已安装Edge浏览器

## 错误处理

- 如果登录失败，请检查账号密码是否正确
- 如果遇到验证码，使用交互式模式手动完成
- 如果cookies加载失败，工具会自动重新登录

## 安全提醒

- 请妥善保管你的配置文件，不要泄露账号密码
- 建议定期更换密码
- cookies文件包含敏感信息，请注意保护
