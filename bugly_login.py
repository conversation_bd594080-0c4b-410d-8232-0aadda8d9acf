import asyncio
import json
import os
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContex<PERSON>, Page
from cookie_manager import CookieManager

class BuglyLogin:
    """Bugly自动登录类"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化登录器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.cookie_manager = CookieManager()
        
        # Bugly OAuth登录URL
        self.login_url = ("https://graph.qq.com/oauth2.0/show?which=Login&display=pc&client_id=101481973"
                         "&redirect_uri=https%3A%2F%2Fcas.bugly.qq.com%2Fcas%2FloginBack%3Ftype%3D9%26sn%3D"
                         "83a17763-355b-403d-964b-515f2f7f26af&response_type=code&state="
                         "f5220e27188631e45a1d34f3960340b5&scope=get_user_info,add_topic,add_one_blog,"
                         "add_album,upload_pic,list_album,add_share,check_page_fans,add_t,add_pic_t,del_t,"
                         "get_repost_list,get_info,get_other_info,get_fanslist,get_idollist,add_idol,del_ido,"
                         "get_tenpay_addr")
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"配置文件未找到: {self.config_file}")
            return {}
        except json.JSONDecodeError:
            print("配置文件格式错误")
            return {}
    
    def _get_login_credentials(self) -> tuple:
        """获取登录凭据"""
        qq_config = self.config.get('qq_login', {})
        username = qq_config.get('username')
        password = qq_config.get('password')
        
        if not username or not password:
            raise ValueError("请在配置文件中设置正确的QQ账号和密码")
        
        return username, password
    
    async def _create_browser_context(self) -> tuple[Browser, BrowserContext]:
        """创建浏览器上下文"""
        playwright = await async_playwright().start()
        
        browser = await playwright.chromium.launch(
            headless=self.config.get('scraper_settings', {}).get('headless', False),
            channel="msedge",
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        return browser, context
    
    async def _perform_login(self, page: Page, username: str, password: str) -> bool:
        """执行登录操作"""
        try:
            # 等待页面加载
            await page.wait_for_timeout(3000)
            
            # 查找登录iframe
            login_frame = await self._find_login_frame(page)
            if login_frame:
                page = login_frame
                await page.wait_for_timeout(2000)
            
            # 切换到密码登录模式
            await self._switch_to_password_login(page)
            
            # 输入用户名
            if not await self._fill_username(page, username):
                return False
            
            # 输入密码
            if not await self._fill_password(page, password):
                return False
            
            # 点击登录按钮
            await self._click_login_button(page)
            
            # 等待登录完成
            await page.wait_for_timeout(5000)
            
            return True
            
        except Exception as e:
            print(f"登录过程中出错: {e}")
            return False
    
    async def _find_login_frame(self, page: Page):
        """查找登录iframe"""
        try:
            iframes = await page.query_selector_all("iframe")
            
            for iframe in iframes:
                frame_src = await iframe.get_attribute("src")
                if frame_src and ("login" in frame_src or "ptlogin" in frame_src):
                    return await iframe.content_frame()
            
            return None
        except Exception:
            return None
    
    async def _switch_to_password_login(self, page: Page):
        """切换到密码登录模式"""
        try:
            password_login_btn = await page.query_selector("#switcher_plogin")
            if password_login_btn and await password_login_btn.is_visible():
                await password_login_btn.click()
                await page.wait_for_timeout(2000)
                print("✓ 已切换到密码登录模式")
        except Exception:
            pass
    
    async def _fill_username(self, page: Page, username: str) -> bool:
        """填入用户名"""
        selectors = ["#u", "input[name='u']", "input[type='text']"]
        
        for selector in selectors:
            try:
                element = await page.query_selector(selector)
                if element and await element.is_visible():
                    await element.fill("")
                    await element.fill(username)
                    print("✓ 已输入用户名")
                    return True
            except Exception:
                continue
        
        print("✗ 未找到用户名输入框")
        return False
    
    async def _fill_password(self, page: Page, password: str) -> bool:
        """填入密码"""
        selectors = ["#p", "input[name='p']", "input[type='password']"]
        
        for selector in selectors:
            try:
                element = await page.query_selector(selector)
                if element and await element.is_visible():
                    await element.fill("")
                    await element.fill(password)
                    print("✓ 已输入密码")
                    return True
            except Exception:
                continue
        
        print("✗ 未找到密码输入框")
        return False
    
    async def _click_login_button(self, page: Page):
        """点击登录按钮"""
        selectors = [
            "#login_button",
            "input[value='登录']",
            "button[type='submit']",
            ".login_button",
            "#loginBtn"
        ]
        
        for selector in selectors:
            try:
                element = await page.query_selector(selector)
                if element and await element.is_visible():
                    await element.click()
                    print("✓ 已点击登录按钮")
                    return
            except Exception:
                continue
        
        print("未找到登录按钮，尝试按回车键")
        await page.keyboard.press("Enter")
    
    async def _check_login_success(self, page: Page) -> bool:
        """检查登录是否成功"""
        try:
            # 等待页面跳转
            await page.wait_for_timeout(3000)

            # 检查URL是否包含成功标识
            current_url = page.url
            success_indicators = ["workbench", "bugly.qq.com/v2", "cas.bugly.qq.com"]

            return any(indicator in current_url for indicator in success_indicators)

        except Exception:
            return False

    async def login(self, save_cookies: bool = True) -> bool:
        """
        执行登录流程

        Args:
            save_cookies: 是否保存cookies

        Returns:
            bool: 登录是否成功
        """
        browser = None
        try:
            # 获取登录凭据
            username, password = self._get_login_credentials()

            # 创建浏览器上下文
            browser, context = await self._create_browser_context()

            # 尝试加载已保存的cookies
            if self.cookie_manager.is_cookies_valid():
                print("发现有效的cookies，尝试使用...")
                await self.cookie_manager.load_cookies(context)

            # 创建页面并访问登录URL
            page = await context.new_page()
            print("正在访问登录页面...")
            await page.goto(self.login_url, wait_until="domcontentloaded", timeout=60000)

            # 检查是否已经登录
            if await self._check_login_success(page):
                print("✓ 检测到已登录状态")
                if save_cookies:
                    await self.cookie_manager.save_cookies(context)
                return True

            # 执行登录
            print("开始执行登录...")
            if not await self._perform_login(page, username, password):
                print("✗ 登录失败")
                return False

            # 检查登录结果
            if await self._check_login_success(page):
                print("✓ 登录成功！")
                if save_cookies:
                    await self.cookie_manager.save_cookies(context)
                return True
            else:
                print("✗ 登录验证失败")
                return False

        except Exception as e:
            print(f"登录过程中出错: {e}")
            return False
        finally:
            if browser:
                await browser.close()

    async def login_interactive(self) -> bool:
        """
        交互式登录（保持浏览器打开）

        Returns:
            bool: 登录是否成功
        """
        browser = None
        try:
            # 获取登录凭据
            username, password = self._get_login_credentials()

            # 创建浏览器上下文
            browser, context = await self._create_browser_context()

            # 尝试加载已保存的cookies
            if self.cookie_manager.is_cookies_valid():
                print("发现有效的cookies，尝试使用...")
                await self.cookie_manager.load_cookies(context)

            # 创建页面并访问登录URL
            page = await context.new_page()
            print("正在访问登录页面...")
            await page.goto(self.login_url, wait_until="domcontentloaded", timeout=60000)

            # 检查是否已经登录
            if await self._check_login_success(page):
                print("✓ 检测到已登录状态")
                await self.cookie_manager.save_cookies(context)
                print("浏览器将保持打开状态，按回车键关闭...")
                input()
                return True

            # 执行登录
            print("开始执行登录...")
            await self._perform_login(page, username, password)

            # 等待用户确认登录完成
            print("请在浏览器中完成登录（如需要验证码等），完成后按回车键...")
            input()

            # 保存cookies
            await self.cookie_manager.save_cookies(context)
            print("✓ 已保存登录状态")

            return True

        except Exception as e:
            print(f"交互式登录过程中出错: {e}")
            return False
        finally:
            if browser:
                await browser.close()
