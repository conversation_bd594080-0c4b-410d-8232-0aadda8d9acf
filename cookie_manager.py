import json
import os
from datetime import datetime
from typing import List, Dict, Optional

class CookieManager:
    """Cookie管理器，负责保存和加载浏览器cookie"""
    
    def __init__(self, cookie_file: str = "bugly_cookies.json"):
        """
        初始化Cookie管理器
        
        Args:
            cookie_file: cookie保存文件路径
        """
        self.cookie_file = cookie_file
        self.cookies_dir = os.path.dirname(os.path.abspath(cookie_file))
        if self.cookies_dir and not os.path.exists(self.cookies_dir):
            os.makedirs(self.cookies_dir)
    
    async def save_cookies(self, context, domain: str = "bugly.qq.com") -> bool:
        """
        保存浏览器上下文中的cookies
        
        Args:
            context: Playwright浏览器上下文
            domain: 要保存的域名
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 获取所有cookies
            cookies = await context.cookies()
            
            # 过滤指定域名的cookies
            filtered_cookies = [
                cookie for cookie in cookies 
                if domain in cookie.get('domain', '') or 'qq.com' in cookie.get('domain', '')
            ]
            
            # 准备保存的数据
            cookie_data = {
                'domain': domain,
                'saved_at': datetime.now().isoformat(),
                'cookies': filtered_cookies
            }
            
            # 保存到文件
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)
            
            print(f"✓ 已保存 {len(filtered_cookies)} 个cookies到 {self.cookie_file}")
            return True
            
        except Exception as e:
            print(f"✗ 保存cookies失败: {e}")
            return False
    
    async def load_cookies(self, context) -> bool:
        """
        从文件加载cookies到浏览器上下文
        
        Args:
            context: Playwright浏览器上下文
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.cookie_file):
                print(f"Cookie文件不存在: {self.cookie_file}")
                return False
            
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            cookies = cookie_data.get('cookies', [])
            if not cookies:
                print("Cookie文件中没有有效的cookies")
                return False
            
            # 加载cookies到浏览器上下文
            await context.add_cookies(cookies)
            
            saved_at = cookie_data.get('saved_at', '未知')
            print(f"✓ 已加载 {len(cookies)} 个cookies (保存时间: {saved_at})")
            return True
            
        except Exception as e:
            print(f"✗ 加载cookies失败: {e}")
            return False
    
    def is_cookies_valid(self, max_age_hours: int = 24) -> bool:
        """
        检查cookies是否仍然有效（基于保存时间）
        
        Args:
            max_age_hours: cookies的最大有效时间（小时）
            
        Returns:
            bool: cookies是否有效
        """
        try:
            if not os.path.exists(self.cookie_file):
                return False
            
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            saved_at_str = cookie_data.get('saved_at')
            if not saved_at_str:
                return False
            
            saved_at = datetime.fromisoformat(saved_at_str)
            now = datetime.now()
            age_hours = (now - saved_at).total_seconds() / 3600
            
            return age_hours < max_age_hours
            
        except Exception as e:
            print(f"检查cookies有效性时出错: {e}")
            return False
    
    def clear_cookies(self) -> bool:
        """
        清除保存的cookies文件
        
        Returns:
            bool: 清除是否成功
        """
        try:
            if os.path.exists(self.cookie_file):
                os.remove(self.cookie_file)
                print(f"✓ 已清除cookies文件: {self.cookie_file}")
                return True
            else:
                print("Cookies文件不存在，无需清除")
                return True
        except Exception as e:
            print(f"✗ 清除cookies失败: {e}")
            return False
