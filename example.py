#!/usr/bin/env python3
"""
Bugly登录使用示例

展示如何在代码中使用BuglyLogin类
"""

import asyncio
from bugly_login import BuglyLogin
from cookie_manager import <PERSON>ieManager

async def example_basic_login():
    """基本登录示例"""
    print("=== 基本登录示例 ===")
    
    login = BuglyLogin()
    success = await login.login()
    
    if success:
        print("登录成功！cookies已保存")
    else:
        print("登录失败")

async def example_check_cookies():
    """检查cookies有效性示例"""
    print("=== 检查cookies有效性 ===")
    
    cookie_manager = CookieManager()
    
    if cookie_manager.is_cookies_valid():
        print("cookies仍然有效")
    else:
        print("cookies已过期或不存在")

async def example_clear_cookies():
    """清除cookies示例"""
    print("=== 清除cookies示例 ===")
    
    cookie_manager = CookieManager()
    success = cookie_manager.clear_cookies()
    
    if success:
        print("cookies已清除")
    else:
        print("清除cookies失败")

async def main():
    """主函数"""
    print("Bugly登录使用示例")
    print("=" * 30)
    
    # 检查cookies状态
    await example_check_cookies()
    print()
    
    # 执行登录
    await example_basic_login()
    print()
    
    # 再次检查cookies状态
    await example_check_cookies()

if __name__ == "__main__":
    asyncio.run(main())
