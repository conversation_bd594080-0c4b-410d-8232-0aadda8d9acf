#!/usr/bin/env python3
"""
Bugly自动登录工具

使用方法:
    python main.py              # 自动登录并保存cookies
    python main.py --interactive # 交互式登录（保持浏览器打开）
    python main.py --clear       # 清除保存的cookies
"""

import asyncio
import argparse
from bugly_login import BuglyLogin
from cookie_manager import CookieManager

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Bugly自动登录工具")
    parser.add_argument(
        "--interactive", 
        action="store_true", 
        help="交互式登录（保持浏览器打开）"
    )
    parser.add_argument(
        "--clear", 
        action="store_true", 
        help="清除保存的cookies"
    )
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_arguments()
    
    # 清除cookies
    if args.clear:
        cookie_manager = CookieManager()
        cookie_manager.clear_cookies()
        return
    
    # 创建登录器
    login = BuglyLogin()
    
    try:
        if args.interactive:
            # 交互式登录
            print("=== Bugly交互式登录 ===")
            success = await login.login_interactive()
        else:
            # 自动登录
            print("=== Bugly自动登录 ===")
            success = await login.login()
        
        if success:
            print("\n✓ 登录流程完成")
        else:
            print("\n✗ 登录失败")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n程序执行出错: {e}")

if __name__ == "__main__":
    print("Bugly自动登录工具 v1.0")
    print("=" * 40)
    asyncio.run(main())
