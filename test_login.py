import asyncio
from playwright.async_api import async_playwright
import json
import os

def load_config():
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"配置文件未找到: {config_path}")
        print("请先配置 config.json 文件中的QQ账号和密码")
        return None
    except json.JSONDecodeError:
        print("配置文件格式错误，请检查 config.json")
        return None

async def test_qq_login():
    """测试QQ登录功能"""
    # 加载配置文件
    config = load_config()
    if not config:
        return
    
    qq_config = config.get('qq_login', {})
    username = qq_config.get('username')
    password = qq_config.get('password')
    
    if not username or not password or username == "your_qq_number" or password == "your_password":
        print("请先在 config.json 中配置正确的QQ账号和密码")
        return
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            channel="msedge",
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        page = await context.new_page()
        
        try:
            print("正在访问Bugly登录页面...")
            await page.goto("https://bugly.qq.com/v2/workbench/apps", wait_until="networkidle")
            await page.wait_for_timeout(3000)
            
            # 打印当前页面信息
            current_url = page.url
            page_title = await page.title()
            print(f"当前页面URL: {current_url}")
            print(f"当前页面标题: {page_title}")
            
            # 截图保存当前页面状态
            await page.screenshot(path="test_login_page.png")
            print("已保存登录页面截图: test_login_page.png")
            
            # 检查是否需要登录
            if "login" in current_url or "connect.qq.com" in current_url or page.url != "https://bugly.qq.com/v2/workbench/apps":
                print("检测到需要登录，开始测试自动登录...")
                
                # 查找所有输入框
                all_inputs = await page.query_selector_all("input")
                print(f"页面中找到 {len(all_inputs)} 个输入框")
                
                for i, input_elem in enumerate(all_inputs):
                    input_type = await input_elem.get_attribute("type") or "text"
                    input_name = await input_elem.get_attribute("name")
                    input_id = await input_elem.get_attribute("id")
                    input_placeholder = await input_elem.get_attribute("placeholder")
                    is_visible = await input_elem.is_visible()
                    print(f"输入框 {i+1}: type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}, visible={is_visible}")
                
                # 检查是否为QQ OAuth授权页面
                if "graph.qq.com" in current_url:
                    print("\n检测到QQ OAuth授权页面，查找登录相关元素...")
                    
                    # 在OAuth页面查找登录相关元素
                    await page.wait_for_timeout(2000)
                    
                    # 查找iframe中的登录表单
                    iframes = await page.query_selector_all("iframe")
                    print(f"找到 {len(iframes)} 个iframe")
                    
                    login_frame = None
                    for i, iframe in enumerate(iframes):
                        try:
                            frame_src = await iframe.get_attribute("src")
                            frame_id = await iframe.get_attribute("id")
                            print(f"iframe {i+1}: src={frame_src}, id={frame_id}")
                            
                            if frame_src and ("login" in frame_src or "ptlogin" in frame_src):
                                login_frame = await iframe.content_frame()
                                print(f"找到登录iframe: {frame_src}")
                                break
                        except Exception as e:
                            print(f"检查iframe {i+1}时出错: {e}")
                            continue
                    
                    if login_frame:
                        print("在iframe中查找登录元素...")
                        page = login_frame  # 切换到iframe上下文
                        await page.wait_for_timeout(2000)
                        
                        # 重新获取iframe中的输入框信息
                        all_inputs = await page.query_selector_all("input")
                        print(f"\niframe中找到 {len(all_inputs)} 个输入框")
                        for i, input_elem in enumerate(all_inputs):
                            input_type = await input_elem.get_attribute("type") or "text"
                            input_name = await input_elem.get_attribute("name")
                            input_id = await input_elem.get_attribute("id")
                            input_placeholder = await input_elem.get_attribute("placeholder")
                            is_visible = await input_elem.is_visible()
                            print(f"iframe输入框 {i+1}: type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}, visible={is_visible}")
                    else:
                        print("未找到登录iframe，在主页面查找登录元素...")
                
                # 查找并点击密码登录按钮
                print("\n尝试点击密码登录按钮...")
                try:
                    password_login_btn = await page.query_selector("#switcher_plogin")
                    if password_login_btn:
                        is_visible = await password_login_btn.is_visible()
                        if is_visible:
                            await password_login_btn.click()
                            await page.wait_for_timeout(2000)
                            print("✓ 已切换到密码登录模式")
                            
                            # 重新截图查看切换后的状态
                            await page.screenshot(path="test_after_switch.png")
                            print("已保存切换后截图: test_after_switch.png")
                        else:
                            print("✗ 密码登录按钮不可见")
                    else:
                        print("✗ 未找到密码登录切换按钮")
                except Exception as e:
                    print(f"✗ 点击密码登录按钮时出错: {e}")
                
                # 查找用户名输入框
                print("\n尝试查找用户名输入框...")
                username_input = None
                username_selectors = ["#u", "input[name='u']", "input[type='text']"]
                
                for selector in username_selectors:
                    try:
                        username_input = await page.query_selector(selector)
                        if username_input:
                            is_visible = await username_input.is_visible()
                            if is_visible:
                                print(f"✓ 找到用户名输入框: {selector}")
                                await username_input.fill("")
                                await username_input.fill(username)
                                print("✓ 已输入用户名")
                                break
                    except Exception as e:
                        print(f"✗ 尝试 {selector} 时出错: {e}")
                        continue
                
                if not username_input:
                    print("✗ 未找到可用的用户名输入框")
                    return
                
                # 查找密码输入框
                print("\n尝试查找密码输入框...")
                password_input = None
                password_selectors = ["#p", "input[name='p']", "input[type='password']"]
                
                for selector in password_selectors:
                    try:
                        password_input = await page.query_selector(selector)
                        if password_input:
                            is_visible = await password_input.is_visible()
                            if is_visible:
                                print(f"✓ 找到密码输入框: {selector}")
                                await password_input.fill("")
                                await password_input.fill(password)
                                print("✓ 已输入密码")
                                break
                    except Exception as e:
                        print(f"✗ 尝试 {selector} 时出错: {e}")
                        continue
                
                if not password_input:
                    print("✗ 未找到可用的密码输入框")
                    return
                
                # 查找并点击登录按钮
                print("\n尝试查找登录按钮...")
                login_button_selectors = [
                    "#login_button",
                    "input[value='登录']",
                    "button[type='submit']",
                    ".login_button",
                    "#loginBtn",
                    "button:has-text('登录')"
                ]
                
                login_button = None
                for selector in login_button_selectors:
                    try:
                        login_button = await page.query_selector(selector)
                        if login_button:
                            is_visible = await login_button.is_visible()
                            if is_visible:
                                print(f"✓ 找到登录按钮: {selector}")
                                await login_button.click()
                                print("✓ 已点击登录按钮")
                                break
                    except Exception as e:
                        print(f"✗ 尝试 {selector} 时出错: {e}")
                        continue
                
                if not login_button:
                    print("✗ 未找到登录按钮，尝试按回车键")
                    await password_input.press("Enter")
                
                # 等待登录完成
                print("\n等待登录完成...")
                await page.wait_for_timeout(5000)
                
                # 检查登录结果
                final_url = page.url
                print(f"登录后URL: {final_url}")
                
                if "workbench" in final_url or "bugly.qq.com/v2" in final_url:
                    print("✓ 登录成功！")
                    # 如果当前page是frame，需要切换回主页面进行截图
                    if hasattr(page, 'page'):
                        main_page = page.page
                    else:
                        main_page = browser.pages[0] if browser.pages else page
                    
                    await main_page.screenshot(path="test_login_success.png")
                    print("已保存登录成功截图: test_login_success.png")
                else:
                    print("✗ 登录可能失败或需要额外验证")
                    # 如果当前page是frame，需要切换回主页面进行截图
                    if hasattr(page, 'page'):
                        main_page = page.page
                    else:
                        main_page = browser.pages[0] if browser.pages else page
                    
                    await main_page.screenshot(path="test_login_failed.png")
                    print("已保存登录失败截图: test_login_failed.png")
                    
                    # 检查是否有验证码
                    captcha_elements = await page.query_selector_all("#tcaptcha_iframe, .captcha, .verify, [id*='captcha'], [class*='captcha']")
                    if captcha_elements:
                        print("检测到验证码，需要手动完成")
                        input("请手动完成验证后按回车继续: ")
            else:
                print("已登录状态，无需重新登录")
                
        except Exception as e:
            print(f"测试过程中出错: {e}")
            try:
                # 如果当前page是frame，需要切换回主页面进行截图
                if hasattr(page, 'page'):
                    main_page = page.page
                else:
                    main_page = browser.pages[0] if browser.pages else page
                
                await main_page.screenshot(path="test_error.png")
                print("已保存错误截图: test_error.png")
            except:
                pass
        finally:
            print("\n测试完成，浏览器将保持打开状态以便查看结果")
            print("按回车键关闭浏览器...")
            input()
            await browser.close()

if __name__ == "__main__":
    print("开始测试Bugly自动登录功能...")
    asyncio.run(test_qq_login())